import React, { useLayoutEffect, useRef, useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import { FirebaseService } from '@/services/firebase';
import { Destination } from '@/types/firebase';
import { useQuery } from '@tanstack/react-query';

// --- Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

const Destinations = () => {
  const main = useRef();

  // Use React Query for better caching and performance
  const { data: destinations = [], isLoading: loading } = useQuery({
    queryKey: ['destinations'],
    queryFn: () => FirebaseService.getDestinations(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes (renamed from cacheTime in newer versions)
  });

  useLayoutEffect(() => {
    const ctx = gsap.context(() => {
      // --- Create the smooth scroller (simplified version without ScrollSmoother)
      // We'll use regular scrolling with GSAP animations

      // --- Header animation (for existing website header) - Fixed positioning
      const tlHeader = gsap.timeline({ delay: 0.5 });
      tlHeader
        .fromTo('header',
          { y: 0, opacity: 1 },
          { y: 0, opacity: 1, duration: 0.1, ease: 'none' }
        );

      // --- Intro section animation
      const tlIntro = gsap.timeline({ delay: 1.2 });
      tlIntro
        .from('.intro-line', { y: 400, ease: 'power4', duration: 3 })
        .from('.intro__txt', { x: -100, opacity: 0, ease: 'power4', duration: 3 }, 0.7)
        .from('.intro__img--1', { y: 50, opacity: 0, ease: 'power2', duration: 10 }, 1)
        .from('.intro__img--2', { y: -50, opacity: 0, ease: 'power2', duration: 10 }, 1);

      // --- Intro scroll out animation
      gsap.timeline({
        scrollTrigger: {
          trigger: '.intro',
          scrub: 1,
          start: "top bottom",
          end: "bottom top",
        },
      })
      .to('.intro__title', { x: 400, ease: 'power4.in' })
      .to('.intro__txt', { y: 100, ease: 'power4.in' }, 0);

      // --- Animate slides on scroll
      const slides = gsap.utils.toArray('.slide') as Element[];
      slides.forEach((slide) => {
        const tlSlide = gsap.timeline({
          scrollTrigger: {
            trigger: slide as gsap.DOMTarget,
            start: "40% 50%",
          },
        });

        tlSlide
          .from(slide.querySelectorAll('.col__content-title'), { ease: "power4", y: "+=5vh", duration: 2.5 })
          .from(slide.querySelectorAll('.line__inner'), { y: 200, duration: 2, ease: "power4", stagger: 0.1 }, 0)
          .from(slide.querySelectorAll('.col__content-txt'), { x: 100, y: 50, opacity: 0, duration: 2, ease: "power4" }, 0.4)
          .from(slide.querySelectorAll('.slide-link'), { x: -100, y: 100, opacity: 0, duration: 2, ease: "power4" }, 0.3)
          .from(slide.querySelectorAll('.slide__scroll-link'), { y: 200, duration: 3, ease: "power4" }, 0.4)
          .to(slide.querySelectorAll('.slide__scroll-line'), { scaleY: 0.6, transformOrigin: "bottom left", duration: 2.5, ease: "elastic(1,0.5)" }, 1.4);
      });

      // --- Parallax for images
      const imageWrappers = gsap.utils.toArray('.col__image-wrap') as Element[];
      imageWrappers.forEach((wrapper) => {
        const image = wrapper.querySelector('img');
        if (image) {
          gsap.fromTo(
            image,
            { y: "-15vh" },
            {
              y: "15vh",
              scrollTrigger: {
                trigger: wrapper as gsap.DOMTarget,
                scrub: true,
                start: "top bottom",
              },
              ease: 'none',
            }
          );
        }
      });

      // --- Footer animation (for website footer)
      gsap.from('#footer', {
        scrollTrigger: {
          trigger: '#footer',
          start: "top 90%",
          toggleActions: "play none none reverse",
        },
        y: 50,
        opacity: 0,
        duration: 1,
        ease: 'power2.out'
      });

    }, main); // <-- scope animations to the main element

    return () => ctx.revert(); // <-- cleanup
  }, [destinations]); // Re-run when destinations change

  // --- Event Handlers
  const handleScrollTo = (target: string) => {
    gsap.to(window, { duration: 1, scrollTo: target });
  };

  // Utility function to truncate text
  const truncateText = (text: string, maxWords: number = 25) => {
    const words = text.split(' ');
    if (words.length <= maxWords) return text;
    return words.slice(0, maxWords).join(' ') + '...';
  };



  // Generic handler for link mouse animations
  const handleLinkMouseOver = (e: React.MouseEvent) => {
    gsap.to(e.currentTarget.querySelector('.slide-link__line'), {
      x: 20, scaleX: 0.3, transformOrigin: "right center", duration: 0.8, ease: "power4"
    });
  };
  const handleLinkMouseOut = (e: React.MouseEvent) => {
    gsap.to(e.currentTarget.querySelector('.slide-link__line'), {
      x: 0, scaleX: 1, transformOrigin: "right center", duration: 0.8, ease: "power4"
    });
  };

  // Prepare destination data for slides
  const slideData = useMemo(() => {
    if (!destinations || destinations.length === 0) {
      // Return sample destinations if none are loaded
      return [
        {
          id: 1,
          title: ['Serengeti', 'No. 1'],
          bgColor: 'bg-slide1-bg',
          bgColorT: 'md:bg-slide1-bg bg-slide1-bg-t',
          scrollLineBg: 'bg-slide1-bg',
          destination: {
            id: '1',
            name: 'Serengeti National Park',
            description: 'Experience the Great Migration and endless plains of the Serengeti, home to millions of wildebeest, zebras, and predators in Africa\'s most iconic wildlife sanctuary.',
            images: [
              'photo-1472396961693-142e6e269027',
              'photo-1547036967-23d11aacaee0',
              'image%20(1).png'
            ]
          }
        },
        {
          id: 2,
          title: ['Ngorongoro', 'No. 2'],
          bgColor: 'bg-slide2-bg',
          bgColorT: 'md:bg-slide2-bg bg-slide2-bg-t',
          scrollLineBg: 'bg-slide2-bg',
          destination: {
            id: '2',
            name: 'Ngorongoro Crater',
            description: 'Discover the world\'s largest intact volcanic caldera, a UNESCO World Heritage Site teeming with wildlife in this natural amphitheater of extraordinary beauty.',
            images: [
              'photo-1466721591366-2d5fba72006d',
              'photo-1493962853295-0fd70327578a',
              'image%20(2).png'
            ]
          }
        },
        {
          id: 3,
          title: ['Tarangire', 'No. 3'],
          bgColor: 'bg-slide3-bg',
          bgColorT: 'md:bg-slide3-bg bg-slide3-bg-t',
          scrollLineBg: 'bg-slide3-bg',
          destination: {
            id: '3',
            name: 'Tarangire National Park',
            description: 'Famous for its large elephant herds and iconic baobab trees, offering spectacular wildlife viewing in a landscape of ancient giants and diverse ecosystems.',
            images: [
              'photo-1547471080-7cc2caa01a7e',
              'photo-1516426122078-c23e76319801',
              'image%20(3).png'
            ]
          }
        },
        {
          id: 4,
          title: ['Manyara', 'No. 4'],
          bgColor: 'bg-slide4-bg',
          bgColorT: 'md:bg-slide4-bg bg-slide4-bg-t',
          scrollLineBg: 'bg-slide4-bg',
          destination: {
            id: '4',
            name: 'Lake Manyara National Park',
            description: 'Renowned for its tree-climbing lions and diverse birdlife, this compact park offers incredible wildlife density in a stunning alkaline lake setting.',
            images: [
              'photo-1534177616072-ef7dc120449d',
              'photo-1551632436-cbf8dd35adfa',
              'image%20(4).png'
            ]
          }
        },
        {
          id: 5,
          title: ['Ruaha', 'No. 5'],
          bgColor: 'bg-slide5-bg',
          bgColorT: 'md:bg-slide5-bg bg-slide5-bg-t',
          scrollLineBg: 'bg-slide5-bg',
          destination: {
            id: '5',
            name: 'Ruaha National Park',
            description: 'Tanzania\'s largest national park, offering pristine wilderness experiences with exceptional predator sightings and dramatic landscapes.',
            images: [
              'photo-1549366021-9f761d040a94',
              'photo-1518709268805-4e9042af2176',
              'image%20(5).png'
            ]
          }
        },
        {
          id: 6,
          title: ['Selous', 'No. 6'],
          bgColor: 'bg-slide6-bg',
          bgColorT: 'md:bg-slide6-bg bg-slide6-bg-t',
          scrollLineBg: 'bg-slide6-bg',
          destination: {
            id: '6',
            name: 'Selous Game Reserve',
            description: 'One of Africa\'s largest protected areas, offering boat safaris, walking safaris, and exceptional wildlife viewing in untouched wilderness.',
            images: [
              'photo-1564760055775-d63b17a55c44',
              'photo-1571019613454-1cb2f99b2d8b',
              'image%20(6).png'
            ]
          }
        }
      ];
    }

    return (destinations as Destination[]).slice(0, 6).map((destination, index) => ({
      id: index + 1,
      title: [destination.name?.split(' ')[0] || 'Destination', `No. ${index + 1}`],
      bgColor: `bg-slide${(index % 6) + 1}-bg`,
      bgColorT: `md:bg-slide${(index % 6) + 1}-bg bg-slide${(index % 6) + 1}-bg-t`,
      scrollLineBg: `bg-slide${(index % 6) + 1}-bg`,
      destination
    }));
  }, [destinations]);

  if (loading) {
    return (
      <PageLoader
        title="Loading Destinations..."
        subtitle="Discovering Tanzania's wilderness..."
      />
    );
  }

  return (
    <div ref={main} className="bg-[#16191D] font-['Open_Sans'] text-[#F2EEE6] text-[14px] leading-[1.3] antialiased luxury-scrollbar">

      {/* Website Header */}
      <Header />

      <div id="smooth-wrapper" className="relative">
        <div id="smooth-content" className="relative bg-[#16191D] visible">

          {/* Luxury Hero Section - Following Contact Page Design */}
          <section className="intro slide--0 relative overflow-hidden bg-[#16191D] pt-20 sm:pt-24 min-h-screen" id="slide-0">
            {/* Elegant Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                                 radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
                backgroundSize: '40px 40px'
              }} />
            </div>

            {/* Hero Content */}
            <div className="relative z-10 container mx-auto px-4 py-12 sm:py-16 md:py-20 lg:py-24 text-center">
              <div className="max-w-4xl mx-auto">
                {/* Luxury Badge */}
                <div className="inline-flex items-center gap-1.5 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-3 sm:px-4 md:px-6 py-1.5 sm:py-2 mb-6 sm:mb-8">
                  <div className="w-3 h-3 sm:w-4 sm:h-4 bg-[#D4C2A4] rounded-full animate-pulse"></div>
                  <span className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wider uppercase">Exclusive Safari Destinations</span>
                </div>

                <h1 className="intro__title font-cormorant text-3xl sm:text-4xl md:text-5xl lg:text-7xl xl:text-8xl text-[#F2EEE6] mb-4 sm:mb-6 leading-tight overflow-hidden">
                  <span className="intro-line block text-[#F2EEE6]">Discover</span>
                  <span className="intro-line block text-[#D4C2A4] italic">Tanzania</span>
                </h1>

                <p className="intro__txt font-open-sans text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/80 max-w-xl sm:max-w-2xl mx-auto leading-relaxed mb-6 sm:mb-8 px-2">
                  Embark on an extraordinary journey through Tanzania's most magnificent wilderness sanctuaries.
                  <span className="hidden sm:inline"> From the legendary Serengeti plains to the pristine Ngorongoro Crater, discover destinations where nature's grandest spectacles unfold.</span>
                </p>

                {/* Elegant Divider */}
                <div className="flex items-center justify-center gap-2 sm:gap-4 mb-8 sm:mb-12">
                  <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
                  <div className="w-4 h-4 sm:w-6 sm:h-6 bg-[#D4C2A4] rounded-full"></div>
                  <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
                </div>
              </div>
            </div>

            {/* Elegant Scroll Indicator - Mobile Responsive */}
            <div className="absolute bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-[3]">
              <div className="w-5 h-8 sm:w-6 sm:h-10 border-2 border-[#D4C2A4]/50 rounded-full flex justify-center">
                <div className="w-0.5 h-2 sm:w-1 sm:h-3 bg-[#D4C2A4] rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
            </div>
          </section>

          {/* Luxury Destination Slides - Mobile Optimized */}
          {slideData.map((slide: any, index: number) => (
            <section key={slide.id} className={`slide slide--${slide.id} relative min-h-screen sm:h-screen overflow-hidden bg-[#16191D]`} id={`slide-${slide.id}`}>
              {/* Background Image with Overlay - Clickable */}
              <Link to={`/destinations/${slide.destination.id}`} className="absolute inset-0 w-full h-full group cursor-pointer">
                <img
                  className="img object-cover w-full h-full transition-transform duration-700 group-hover:scale-105"
                  src={
                    slide.destination.images && slide.destination.images.length > 0
                      ? slide.destination.images[0].startsWith('http')
                        ? slide.destination.images[0]
                        : slide.destination.images[0].startsWith('image%20')
                          ? `https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/${slide.destination.images[0]}`
                          : `https://images.unsplash.com/${slide.destination.images[0]}?auto=format&fit=crop&w=1920&h=1080`
                      : `https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(${(index % 10) + 1}).png`
                  }
                  alt={slide.destination.name || `Destination ${slide.id}`}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    // First fallback: try a different image from the same destination
                    if (slide.destination.images && slide.destination.images.length > 1) {
                      const currentSrc = target.src;
                      const nextImageIndex = slide.destination.images.findIndex((img: string) =>
                        currentSrc.includes(img)
                      ) + 1;
                      if (nextImageIndex < slide.destination.images.length) {
                        const nextImage = slide.destination.images[nextImageIndex];
                        target.src = nextImage.startsWith('http')
                          ? nextImage
                          : `https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/${nextImage}`;
                        return;
                      }
                    }
                    // Final fallback: use default image
                    target.src = `https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(${(index % 10) + 1}).png`;
                  }}
                />
                {/* Luxury Gradient Overlay - Mobile Enhanced */}
                <div className="absolute inset-0 bg-gradient-to-b sm:bg-gradient-to-r from-[#16191D]/95 via-[#16191D]/70 to-[#16191D]/40 sm:from-[#16191D]/90 sm:via-[#16191D]/60 sm:to-[#16191D]/30 group-hover:from-[#16191D]/80 group-hover:via-[#16191D]/50 group-hover:to-[#16191D]/20 transition-all duration-500"></div>

                {/* Click Indicator - Mobile Responsive */}
                <div className="absolute top-4 right-4 sm:top-8 sm:right-8 w-8 h-8 sm:w-12 sm:h-12 bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                  <div className="w-4 h-4 sm:w-6 sm:h-6 border border-[#D4C2A4] rounded-full group-hover:rotate-90 transition-transform duration-300">
                    <div className="w-1 h-1 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-0.5 ml-0.5 sm:mt-1 sm:ml-1"></div>
                  </div>
                </div>

                {/* Image Loading Indicator */}
                <div className="absolute inset-0 bg-[#16191D] flex items-center justify-center opacity-0 transition-opacity duration-300" id={`loading-${slide.id}`}>
                  <div className="w-8 h-8 sm:w-12 sm:h-12 border-2 border-[#D4C2A4]/30 border-t-[#D4C2A4] rounded-full animate-spin"></div>
                </div>
              </Link>

              {/* Luxury Content Container - Mobile First */}
              <div className="relative z-[2] min-h-screen sm:h-full flex items-end sm:items-center pb-8 sm:pb-0">
                <div className="container mx-auto px-4 sm:px-6 lg:px-12">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-center">

                    {/* Content Column - Mobile Optimized */}
                    <div className={`col__content col__content--${slide.id} ${index % 2 === 0 ? 'lg:order-1' : 'lg:order-2'} order-1 lg:order-${index % 2 === 0 ? '1' : '2'}`}>
                      {/* Destination Number Badge - Mobile Responsive */}
                      <div className="inline-flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6 lg:mb-8 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20">
                        <span className="text-[#D4C2A4] font-['Open_Sans'] text-xs tracking-wider uppercase">
                          Destination {slide.title[1]}
                        </span>
                      </div>

                      {/* Elegant Title - Mobile First */}
                      <h2 className="col__content-title font-['Cormorant_Garamond'] text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-light leading-none mb-4 sm:mb-6 lg:mb-8 overflow-hidden">
                        <span className="line overflow-hidden">
                          <span className="line__inner block text-[#F2EEE6]">{slide.title[0]}</span>
                        </span>
                        <span className="line overflow-hidden -mt-1 sm:-mt-2">
                          <span className="line__inner block text-[#D4C2A4] italic">National Park</span>
                        </span>
                      </h2>

                      {/* Sophisticated Description - Mobile Optimized with Truncation */}
                      <div className="col__content-wrap space-y-4 sm:space-y-6 lg:space-y-8">
                        <p className="col__content-txt font-['Open_Sans'] text-sm xs:text-base sm:text-lg md:text-xl leading-relaxed text-[#F2EEE6]/90 max-w-full sm:max-w-2xl">
                          {truncateText(slide.destination.description || 'Experience the wild beauty and diverse wildlife of this incredible destination in Tanzania, where nature\'s most spectacular moments unfold in pristine wilderness.', 20)}
                        </p>

                        {/* Luxury Action Buttons - Mobile First */}
                        <div className="space-y-3 sm:space-y-4 lg:space-y-6">

                          {/* Secondary CTA - Mobile Stack */}
                          <div className="flex flex-col xs:flex-row items-center gap-2 xs:gap-3 sm:gap-4">
                            <Link
                              to={`/destinations/${slide.destination.id}`}
                              className="group inline-flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transform hover:-translate-y-1 w-full xs:w-auto justify-center"
                            >
                              <span>View Details</span>
                              <div className="w-3 h-3 sm:w-4 sm:h-4 border border-current rounded-full group-hover:scale-110 transition-transform duration-300"></div>
                            </Link>

                            <Link
                              to="/tours"
                              className="group inline-flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transform hover:-translate-y-1 w-full xs:w-auto justify-center"
                            >
                              <span>Book Safari</span>
                              <div className="w-3 h-3 sm:w-4 sm:h-4 bg-current rounded-full group-hover:rotate-90 transition-transform duration-300"></div>
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Glass Morphism Info Card - Mobile First */}
                    <div className={`${index % 2 === 0 ? 'lg:order-2' : 'lg:order-1'} order-2 lg:order-${index % 2 === 0 ? '2' : '1'} flex justify-center lg:justify-${index % 2 === 0 ? 'end' : 'start'} mb-6 lg:mb-0`}>
                      <div className="bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8 max-w-sm sm:max-w-md w-full">
                        <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6] mb-4 sm:mb-6">
                          {slide.destination.name}
                        </h3>

                        <div className="space-y-2 sm:space-y-3 lg:space-y-4">
                          <div className="flex items-center gap-2 sm:gap-3">
                            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full flex-shrink-0"></div>
                            <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm">World-class wildlife viewing</span>
                          </div>
                          <div className="flex items-center gap-2 sm:gap-3">
                            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full flex-shrink-0"></div>
                            <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm">Luxury safari accommodations</span>
                          </div>
                          <div className="flex items-center gap-2 sm:gap-3">
                            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full flex-shrink-0"></div>
                            <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm">Expert guided experiences</span>
                          </div>
                          <div className="flex items-center gap-2 sm:gap-3">
                            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full flex-shrink-0"></div>
                            <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm">Exclusive access opportunities</span>
                          </div>
                        </div>

                        {/* Elegant CTA - Mobile Optimized */}
                        <div className="mt-4 sm:mt-6 lg:mt-8 pt-4 sm:pt-6 border-t border-[#D4C2A4]/20">
                          <Link
                            to={`/destinations/${slide.destination.id}`}
                            className="block w-full py-2.5 sm:py-3 text-center text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm font-medium tracking-wide uppercase border border-[#D4C2A4]/30 rounded-sm hover:bg-[#D4C2A4]/10 transition-colors duration-300"
                          >
                            View Details
                          </Link>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              </div>

              {/* Luxury Scroll Navigation - Mobile Hidden */}
              <a
                href={`#slide-${slide.id + 1}`}
                onClick={(e) => { e.preventDefault(); handleScrollTo(`#slide-${slide.id + 1}`) }}
                className="slide__scroll-link hidden lg:block absolute right-4 sm:right-8 bottom-4 sm:bottom-8 w-12 h-12 sm:w-16 sm:h-16 bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20 rounded-full overflow-hidden group hover:bg-[#D4C2A4]/20 transition-all duration-300"
              >
                <div className="slide__scroll-line absolute inset-0 flex items-center justify-center">
                  <div className="w-4 h-4 sm:w-6 sm:h-6 border-2 border-[#D4C2A4] rounded-full group-hover:rotate-90 transition-transform duration-300">
                    <div className="w-1 h-1 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-0.5 ml-0.5 sm:mt-1 sm:ml-1"></div>
                  </div>
                </div>
              </a>
            </section>
          ))}

        </div>
      </div>

      {/* Luxury Footer Transition - Mobile Optimized */}
      <div className="relative bg-[#16191D] py-8 sm:py-12 lg:py-16">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          {/* Elegant Divider - Mobile Responsive */}
          <div className="flex items-center justify-center mb-6 sm:mb-8 lg:mb-12">
            <div className="w-16 sm:w-24 lg:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
            <div className="mx-2 sm:mx-4 w-2 h-2 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full"></div>
            <div className="w-16 sm:w-24 lg:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
          </div>

          {/* Luxury CTA - Mobile First */}
          <h2 className="font-['Cormorant_Garamond'] text-2xl xs:text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-light text-[#F2EEE6] mb-4 sm:mb-6">
            Ready to Begin Your
            <span className="block text-[#D4C2A4] italic">Safari Adventure?</span>
          </h2>

          <p className="font-['Open_Sans'] text-sm xs:text-base sm:text-lg text-[#F2EEE6]/80 mb-6 sm:mb-8 lg:mb-10 max-w-xs xs:max-w-sm sm:max-w-2xl mx-auto px-2 sm:px-0">
            Let our expert guides craft your perfect Tanzania experience,
            <span className="hidden sm:inline"> tailored to your dreams and desires.</span>
          </p>

          <div className="flex flex-col xs:flex-row gap-3 sm:gap-4 lg:gap-6 justify-center">
            <button className="w-full xs:w-auto px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-xs sm:text-sm tracking-wide uppercase rounded-sm transition-all duration-500 hover:bg-[#F2EEE6] hover:shadow-2xl hover:shadow-[#D4C2A4]/20 transform hover:-translate-y-1">
              Plan Your Safari
            </button>
            <button className="w-full xs:w-auto px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transform hover:-translate-y-1">
              Contact Expert
            </button>
          </div>
        </div>
      </div>

      {/* Website Footer */}
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Destinations;
